"use client"

import React, { useEffect, useMemo, useState } from "react"
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'

// 在浏览器端按网格切图并打包为 zip 下载
// 批量处理相关类型
interface BatchFile {
  file: File
  id: string
  imageUrl: string
  imgSize: { w: number; h: number } | null
  previewSlices: Array<{ url: string; width: number; height: number }>
  status: 'pending' | 'processing' | 'completed' | 'error'
  error?: string
}

interface BatchProgress {
  current: number
  total: number
  currentFileName: string
}

export default function ImageSplitPage() {
  // 批量处理模式
  const [batchMode, setBatchMode] = useState(false)
  const [files, setFiles] = useState<BatchFile[]>([])
  const [batchProgress, setBatchProgress] = useState<BatchProgress | null>(null)

  // 单文件模式（保持向后兼容）
  const [file, setFile] = useState<File | null>(null)
  const [mode, setMode] = useState<"count" | "grid">("count")
  const [count, setCount] = useState<number>(9) // 总块数 n
  const [rows, setRows] = useState<number>(3)
  const [cols, setCols] = useState<number>(3)
  const [format, setFormat] = useState<"image/png" | "image/jpeg">("image/png")
  const [quality, setQuality] = useState<number>(0.92) // 仅 jpeg 生效
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  // 预览相关
  const [imageUrl, setImageUrl] = useState<string | null>(null)
  const [imgSize, setImgSize] = useState<{ w: number; h: number } | null>(null)
  const [previewing, setPreviewing] = useState(false)
  const [previewSlices, setPreviewSlices] = useState<Array<{ url: string; width: number; height: number }>>([]);
  const [showGrid, setShowGrid] = useState(true)
  // 自定义尺寸相关
  const [useCustomSize, setUseCustomSize] = useState(false)
  const [customWidth, setCustomWidth] = useState<number | "">(200)
  const [customHeight, setCustomHeight] = useState<number | "">(200)
  const [lockAspectRatio, setLockAspectRatio] = useState(true)
  // 智能命名规则相关
  const [useCustomNaming, setUseCustomNaming] = useState(false)
  const [namingTemplate, setNamingTemplate] = useState("slice_{index}_{row}x{col}")
  const [includeOriginalName, setIncludeOriginalName] = useState(true)
  // 拖拽上传相关
  const [isDragOver, setIsDragOver] = useState(false)
  const [dragCounter, setDragCounter] = useState(0)

  // 处理自定义尺寸的宽度变化
  const handleCustomWidthChange = (width: number | "") => {
    setCustomWidth(width)
    if (lockAspectRatio && imgSize && typeof width === "number" && width > 0) {
      const aspectRatio = imgSize.h / imgSize.w
      const calculatedHeight = Math.round(width * aspectRatio)
      setCustomHeight(calculatedHeight)
    }
  }

  // 处理自定义尺寸的高度变化
  const handleCustomHeightChange = (height: number | "") => {
    setCustomHeight(height)
    if (lockAspectRatio && imgSize && typeof height === "number" && height > 0) {
      const aspectRatio = imgSize.w / imgSize.h
      const calculatedWidth = Math.round(height * aspectRatio)
      setCustomWidth(calculatedWidth)
    }
  }

  // 智能命名规则处理函数
  const generateFileName = (
    template: string,
    variables: {
      index: number
      row: number
      col: number
      width: number
      height: number
      timestamp: string
      originalName?: string
    },
    extension: string
  ): string => {
    let fileName = template

    // 替换所有变量
    fileName = fileName.replace(/\{index\}/g, String(variables.index).padStart(3, "0"))
    fileName = fileName.replace(/\{row\}/g, String(variables.row))
    fileName = fileName.replace(/\{col\}/g, String(variables.col))
    fileName = fileName.replace(/\{width\}/g, String(variables.width))
    fileName = fileName.replace(/\{height\}/g, String(variables.height))
    fileName = fileName.replace(/\{width\}x\{height\}/g, `${variables.width}x${variables.height}`)
    fileName = fileName.replace(/\{timestamp\}/g, variables.timestamp)

    // 如果包含原始文件名
    if (includeOriginalName && variables.originalName) {
      const nameWithoutExt = variables.originalName.replace(/\.[^.]+$/, "")
      fileName = fileName.replace(/\{original\}/g, nameWithoutExt)
      // 如果模板中没有 {original}，则在前面添加
      if (!template.includes("{original}") && includeOriginalName) {
        fileName = `${nameWithoutExt}_${fileName}`
      }
    }

    // 清理文件名中的非法字符
    fileName = fileName.replace(/[<>:"/\\|?*]/g, "_")

    return fileName + extension
  }

  // 预览命名效果
  const previewFileName = (template: string): string => {
    const now = new Date()
    const timestamp = now.toISOString().replace(/[:.]/g, "-").slice(0, 19)

    return generateFileName(
      template,
      {
        index: 1,
        row: 1,
        col: 1,
        width: useCustomSize && typeof customWidth === "number" ? customWidth : 400,
        height: useCustomSize && typeof customHeight === "number" ? customHeight : 300,
        timestamp,
        originalName: batchMode ? "example.jpg" : (file?.name || "image.jpg")
      },
      ".png"
    )
  }

  // 预设命名模板
  const namingPresets = [
    { name: "默认", template: "slice_{index}_{row}x{col}" },
    { name: "简洁", template: "{index}" },
    { name: "网格位置", template: "r{row}_c{col}" },
    { name: "尺寸信息", template: "slice_{width}x{height}_{index}" },
    { name: "时间戳", template: "{timestamp}_{index}" },
    { name: "完整信息", template: "slice_{index}_r{row}c{col}_{width}x{height}" }
  ]

  // 批量文件处理函数
  const handleBatchFileUpload = (selectedFiles: FileList | File[]) => {
    const fileArray = Array.from(selectedFiles)
    const imageFiles = fileArray.filter(file => file.type.startsWith('image/'))

    if (imageFiles.length === 0) {
      setError("请选择至少一张图片文件")
      return
    }

    const batchFiles: BatchFile[] = imageFiles.map(file => ({
      file,
      id: `${file.name}-${Date.now()}-${Math.random()}`,
      imageUrl: URL.createObjectURL(file),
      imgSize: null,
      previewSlices: [],
      status: 'pending'
    }))

    setFiles(batchFiles)
    setBatchMode(true)
    setError(null)

    // 异步加载每个图片的尺寸信息
    batchFiles.forEach(async (batchFile) => {
      try {
        const img = await loadImage(batchFile.imageUrl)
        setFiles(prev => prev.map(f =>
          f.id === batchFile.id
            ? { ...f, imgSize: { w: img.width, h: img.height } }
            : f
        ))
      } catch (error) {
        console.error(`Failed to load image ${batchFile.file.name}:`, error)
        setFiles(prev => prev.map(f =>
          f.id === batchFile.id
            ? { ...f, status: 'error', error: '图片加载失败' }
            : f
        ))
      }
    })
  }

  // 移除批量文件
  const removeBatchFile = (id: string) => {
    setFiles(prev => {
      const fileToRemove = prev.find(f => f.id === id)
      if (fileToRemove) {
        URL.revokeObjectURL(fileToRemove.imageUrl)
      }
      return prev.filter(f => f.id !== id)
    })
  }

  // 清空批量文件
  const clearBatchFiles = () => {
    files.forEach(f => URL.revokeObjectURL(f.imageUrl))
    setFiles([])
    setBatchMode(false)
    setBatchProgress(null)
  }

  // 拖拽处理函数
  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragCounter(prev => prev + 1)
    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
      setIsDragOver(true)
    }
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragCounter(prev => {
      const newCounter = prev - 1
      if (newCounter === 0) {
        setIsDragOver(false)
      }
      return newCounter
    })
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(false)
    setDragCounter(0)

    const droppedFiles = Array.from(e.dataTransfer.files)
    const imageFiles = droppedFiles.filter(file => file.type.startsWith('image/'))

    if (imageFiles.length === 0) {
      setError("请拖拽图片文件")
      return
    }

    // 清除之前的错误
    setError(null)

    if (batchMode || imageFiles.length > 1) {
      // 批量模式或多文件：使用批量处理
      if (!batchMode) {
        setBatchMode(true)
        // 清理单文件模式的状态
        setFile(null)
        setImageUrl(null)
        setImgSize(null)
        setPreviewSlices([])
      }
      handleBatchFileUpload(imageFiles)
    } else {
      // 单文件模式：只处理第一个文件
      setFile(imageFiles[0])
    }

    // 滚动到上传区域，提供视觉反馈
    setTimeout(() => {
      const uploadSection = document.getElementById('upload-section')
      if (uploadSection) {
        uploadSection.scrollIntoView({ behavior: 'smooth', block: 'center' })
      }
    }, 100)
  }

  const computedGrid = useMemo(() => {
    if (mode === "grid") return { rows, cols }
    const n = Math.max(1, Math.floor(count || 1))
    const r = Math.floor(Math.sqrt(n))
    const c = Math.ceil(n / r)
    return { rows: r, cols: c }
  }, [mode, count, rows, cols])

  // 根据文件生成对象 URL，并计算原图尺寸
  useEffect(() => {
    if (!file) {
      setImageUrl(null)
      setImgSize(null)
      setPreviewSlices([])
      return
    }
    const url = URL.createObjectURL(file)
    setImageUrl(url)
    let cancelled = false
    ;(async () => {
      try {
        const img = await loadImage(url)
        if (!cancelled) setImgSize({ w: img.width, h: img.height })
      } catch (e) {
        console.error(e)
      }
    })()
    return () => {
      cancelled = true
      URL.revokeObjectURL(url)
    }
  }, [file])

  // 实时生成切片缩略图（PNG 预览，不受导出格式限制），简单防抖
  useEffect(() => {
    if (!file) {
      setPreviewSlices([])
      return
    }
    const { rows, cols } = computedGrid
    if (rows < 1 || cols < 1) {
      setPreviewSlices([])
      return
    }
    let cancelled = false
    setPreviewing(true)
    const timer = setTimeout(async () => {
      try {
        const url = URL.createObjectURL(file)
        const img = await loadImage(url)
        const slices: Array<{ url: string; width: number; height: number }> = []
        for (let r = 0; r < rows; r++) {
          for (let c = 0; c < cols; c++) {
            const { sx, sy, sw, sh } = computeSliceBounds(img.width, img.height, cols, rows, c, r)
            // 生成缩略图预览：较小尺寸以提升性能
            const maxSide = 180
            const scale = Math.min(1, maxSide / Math.max(sw, sh))
            const tw = Math.max(1, Math.round(sw * scale))
            const th = Math.max(1, Math.round(sh * scale))
            const canvas = document.createElement("canvas")
            canvas.width = tw
            canvas.height = th
            const ctx = canvas.getContext("2d")!
            ctx.imageSmoothingQuality = "high"
            ctx.drawImage(img, sx, sy, sw, sh, 0, 0, tw, th)
            const dataUrl = canvas.toDataURL("image/png")

            // 确定显示的尺寸信息
            let displayWidth = sw
            let displayHeight = sh

            if (useCustomSize) {
              displayWidth = typeof customWidth === "number" ? customWidth : sw
              displayHeight = typeof customHeight === "number" ? customHeight : sh
            }

            slices.push({ url: dataUrl, width: displayWidth, height: displayHeight })
          }
        }
        URL.revokeObjectURL(url)
        if (!cancelled) {
          setPreviewSlices(slices)
        }
      } catch (e) {
        console.error(e)
        if (!cancelled) {
          setPreviewSlices([])
        }
      } finally {
        if (!cancelled) setPreviewing(false)
      }
    }, 200)
    return () => {
      cancelled = true
      clearTimeout(timer)
    }
  }, [file, computedGrid, useCustomSize, customWidth, customHeight])

  // 全局拖拽事件处理，防止默认行为
  useEffect(() => {
    const handleGlobalDragOver = (e: DragEvent) => {
      e.preventDefault()
    }

    const handleGlobalDrop = (e: DragEvent) => {
      e.preventDefault()
    }

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isDragOver) {
        setIsDragOver(false)
        setDragCounter(0)
      }
    }

    document.addEventListener('dragover', handleGlobalDragOver)
    document.addEventListener('drop', handleGlobalDrop)
    document.addEventListener('keydown', handleKeyDown)

    return () => {
      document.removeEventListener('dragover', handleGlobalDragOver)
      document.removeEventListener('drop', handleGlobalDrop)
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isDragOver])

  // 叠加网格线（以百分比定位，避免依赖实际像素）
  const gridLines = useMemo(() => {
    const hs = Array.from({ length: Math.max(0, computedGrid.rows - 1) }, (_, i) => ((i + 1) * 100) / computedGrid.rows)
    const vs = Array.from({ length: Math.max(0, computedGrid.cols - 1) }, (_, i) => ((i + 1) * 100) / computedGrid.cols)
    return { hs, vs }
  }, [computedGrid])

  async function splitAndZip() {
    try {
      setError(null)
      if (!file) {
        setError("请先选择一张图片")
        return
      }

      const { rows, cols } = computedGrid
      if (rows < 1 || cols < 1) {
        setError("行列数需大于等于 1")
        return
      }

      setLoading(true)

      const imgUrl = URL.createObjectURL(file)
      const img = await loadImage(imgUrl)

      // 动态导入 jszip（需安装依赖 jszip）
      const JSZip = (await import("jszip")).default
      const zip = new JSZip()

      for (let r = 0; r < rows; r++) {
        for (let c = 0; c < cols; c++) {
          const { sx, sy, sw, sh } = computeSliceBounds(img.width, img.height, cols, rows, c, r)

          // 确定导出尺寸
          let targetWidth: number | undefined
          let targetHeight: number | undefined

          if (useCustomSize) {
            targetWidth = typeof customWidth === "number" ? customWidth : undefined
            targetHeight = typeof customHeight === "number" ? customHeight : undefined
          }

          const blob = await sliceToBlob(img, sx, sy, sw, sh, format, quality, targetWidth, targetHeight)
          const idx = r * cols + c

          // 生成文件名
          const now = new Date()
          const timestamp = now.toISOString().replace(/[:.]/g, "-").slice(0, 19)
          const extension = format === "image/png" ? ".png" : format === "image/jpeg" ? ".jpg" : format === "image/webp" ? ".webp" : ".avif"

          const finalWidth = useCustomSize && targetWidth ? targetWidth : sw
          const finalHeight = useCustomSize && targetHeight ? targetHeight : sh

          let fileName: string
          if (useCustomNaming) {
            fileName = generateFileName(
              namingTemplate,
              {
                index: idx + 1,
                row: r + 1,
                col: c + 1,
                width: finalWidth,
                height: finalHeight,
                timestamp,
                originalName: file?.name
              },
              extension
            )
          } else {
            // 默认命名方式
            const sizeInfo = useCustomSize && targetWidth && targetHeight
              ? `_${targetWidth}x${targetHeight}`
              : `_${sw}x${sh}`
            fileName = `slice_${pad(idx + 1, 3)}_r${r + 1}_c${c + 1}${sizeInfo}${extension}`
          }

          zip.file(fileName, blob)
        }
      }

      const zipBlob = await zip.generateAsync({ type: "blob" })
      const a = document.createElement("a")
      a.href = URL.createObjectURL(zipBlob)
      a.download = `${file.name.replace(/\.[^.]+$/, "")}_slices_${rows}x${cols}.zip`
      document.body.appendChild(a)
      a.click()
      a.remove()
      // 释放对象 URL，避免内存泄漏
      URL.revokeObjectURL(a.href)
      URL.revokeObjectURL(imgUrl)
    } catch (e: any) {
      console.error(e)
      setError(e?.message || "处理失败，请稍后重试")
    } finally {
      setLoading(false)
    }
  }

  // 批量处理函数
  async function batchSplitAndZip() {
    if (files.length === 0) {
      setError("请先选择图片文件")
      return
    }

    try {
      setError(null)
      setLoading(true)
      setBatchProgress({ current: 0, total: files.length, currentFileName: '' })

      const { rows, cols } = computedGrid
      if (rows < 1 || cols < 1) {
        setError("行列数需大于等于 1")
        return
      }

      // 动态导入 jszip
      const JSZip = (await import("jszip")).default
      const masterZip = new JSZip()

      for (let fileIndex = 0; fileIndex < files.length; fileIndex++) {
        const batchFile = files[fileIndex]

        // 更新进度
        setBatchProgress({
          current: fileIndex + 1,
          total: files.length,
          currentFileName: batchFile.file.name
        })

        // 更新文件状态为处理中
        setFiles(prev => prev.map(f =>
          f.id === batchFile.id ? { ...f, status: 'processing' } : f
        ))

        try {
          const img = await loadImage(batchFile.imageUrl)

          // 为每个文件创建一个子文件夹
          const fileName = batchFile.file.name.replace(/\.[^.]+$/, "")
          const fileFolder = masterZip.folder(fileName)

          for (let r = 0; r < rows; r++) {
            for (let c = 0; c < cols; c++) {
              const { sx, sy, sw, sh } = computeSliceBounds(img.width, img.height, cols, rows, c, r)

              // 确定导出尺寸
              let targetWidth: number | undefined
              let targetHeight: number | undefined

              if (useCustomSize) {
                targetWidth = typeof customWidth === "number" ? customWidth : undefined
                targetHeight = typeof customHeight === "number" ? customHeight : undefined
              }

              const blob = await sliceToBlob(img, sx, sy, sw, sh, format, quality, targetWidth, targetHeight)
              const idx = r * cols + c

              // 生成文件名
              const now = new Date()
              const timestamp = now.toISOString().replace(/[:.]/g, "-").slice(0, 19)
              const extension = format === "image/png" ? ".png" : format === "image/jpeg" ? ".jpg" : format === "image/webp" ? ".webp" : ".avif"

              const finalWidth = useCustomSize && targetWidth ? targetWidth : sw
              const finalHeight = useCustomSize && targetHeight ? targetHeight : sh

              let fileName: string
              if (useCustomNaming) {
                fileName = generateFileName(
                  namingTemplate,
                  {
                    index: idx + 1,
                    row: r + 1,
                    col: c + 1,
                    width: finalWidth,
                    height: finalHeight,
                    timestamp,
                    originalName: batchFile.file.name
                  },
                  extension
                )
              } else {
                // 默认命名方式
                const sizeInfo = useCustomSize && targetWidth && targetHeight
                  ? `_${targetWidth}x${targetHeight}`
                  : `_${sw}x${sh}`
                fileName = `slice_${pad(idx + 1, 3)}_r${r + 1}_c${c + 1}${sizeInfo}${extension}`
              }

              fileFolder?.file(fileName, blob)
            }
          }

          // 更新文件状态为完成
          setFiles(prev => prev.map(f =>
            f.id === batchFile.id ? { ...f, status: 'completed' } : f
          ))

        } catch (error: any) {
          console.error(`处理文件 ${batchFile.file.name} 时出错:`, error)

          // 更新文件状态为错误
          setFiles(prev => prev.map(f =>
            f.id === batchFile.id
              ? { ...f, status: 'error', error: error?.message || '处理失败' }
              : f
          ))
        }
      }

      // 生成最终的 ZIP 文件
      const zipBlob = await masterZip.generateAsync({ type: "blob" })
      const a = document.createElement("a")
      a.href = URL.createObjectURL(zipBlob)
      a.download = `batch_slices_${rows}x${cols}_${files.length}files.zip`
      document.body.appendChild(a)
      a.click()
      a.remove()
      URL.revokeObjectURL(a.href)

      setBatchProgress(null)

    } catch (e: any) {
      console.error(e)
      setError(e?.message || "批量处理失败，请稍后重试")
      setBatchProgress(null)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900 relative">
      {/* 简洁的背景装饰 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.03)_1px,transparent_1px)] bg-[size:40px_40px]"></div>
      </div>

      {/* 优化的主要内容区域 */}
      <div
        className="flex-1 relative"
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
      >
        {/* 增强的拖拽覆盖层 */}
        {isDragOver && (
          <div className="absolute inset-0 z-50 bg-gradient-to-br from-blue-500/15 via-indigo-500/15 to-purple-500/15 backdrop-blur-md border-2 border-dashed border-blue-400 flex items-center justify-center animate-in fade-in duration-300">
            <div className="text-center bg-white/95 dark:bg-slate-900/95 rounded-3xl p-12 shadow-2xl border border-blue-200/50 dark:border-blue-800/50 backdrop-blur-sm transform scale-105 transition-transform">
              <div className="text-8xl mb-8 animate-bounce">📁</div>
              <h3 className="text-3xl font-bold text-blue-700 dark:text-blue-300 mb-4">
                松开鼠标上传图片
              </h3>
              <p className="text-blue-600 dark:text-blue-400 text-xl mb-6">
                {batchMode ? "支持多张图片批量上传" : "支持单张或多张图片上传"}
              </p>
              <div className="space-y-3">
                <div className="flex items-center justify-center gap-3 text-base text-blue-500 dark:text-blue-400">
                  <span>支持格式：</span>
                  <div className="flex gap-2">
                    <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900/50 rounded-full font-medium text-sm">PNG</span>
                    <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900/50 rounded-full font-medium text-sm">JPEG</span>
                    <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900/50 rounded-full font-medium text-sm">WebP</span>
                  </div>
                </div>
                <div className="text-sm text-blue-400 dark:text-blue-500 bg-blue-50 dark:bg-blue-950/30 px-4 py-2 rounded-lg">
                  按 ESC 键取消拖拽
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="flex-1 overflow-auto px-6 py-8">
          <div className="max-w-8xl mx-auto">
            <div className="grid grid-cols-1 xl:grid-cols-12 gap-8 items-start">
              {/* 左侧：上传区域 */}
              <div className="xl:col-span-4 space-y-6">
                <Card id="upload-section" className="shadow-sm border bg-white dark:bg-slate-900">
                  <CardHeader>
                    <CardTitle className="text-lg font-semibold text-slate-900 dark:text-white">选择图片</CardTitle>
                    <CardDescription className="text-slate-600 dark:text-slate-300">支持 PNG / JPEG / WEBP，可单张或批量处理</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* 优化的模式切换 */}
                    <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 rounded-xl border border-blue-200/50 dark:border-blue-800/50">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                          <span className="text-white text-sm">⚡</span>
                        </div>
                        <div>
                          <Label className="text-base font-semibold text-slate-800 dark:text-slate-200">批量处理模式</Label>
                          <p className="text-xs text-slate-600 dark:text-slate-400">同时处理多张图片</p>
                        </div>
                      </div>
                      <Switch
                        checked={batchMode}
                        onCheckedChange={(checked) => {
                          setBatchMode(checked)
                          if (!checked) {
                            clearBatchFiles()
                          } else {
                            setFile(null)
                            setImageUrl(null)
                            setImgSize(null)
                            setPreviewSlices([])
                          }
                        }}
                        className="data-[state=checked]:bg-blue-600"
                      />
                    </div>

                      {/* 文件上传 */}
                      <div className="space-y-2">
                        <Label htmlFor="file" className="text-sm font-medium text-slate-700 dark:text-slate-200">
                          {batchMode ? "选择多张图片" : "选择图片"}
                        </Label>

                        {/* 拖拽上传区域 */}
                        <div
                          className={`relative border-2 border-dashed rounded-lg transition-all duration-200 ${
                            isDragOver
                              ? 'border-blue-500 bg-blue-50 dark:bg-blue-950/20 scale-105'
                              : 'border-slate-300 dark:border-slate-600 hover:border-blue-400 dark:hover:border-blue-500 hover:bg-slate-50 dark:hover:bg-slate-800/50'
                          } ${
                            (!batchMode && !file) || (batchMode && files.length === 0)
                              ? 'p-8'
                              : 'p-4'
                          }`}
                          onDragEnter={handleDragEnter}
                          onDragLeave={handleDragLeave}
                          onDragOver={handleDragOver}
                          onDrop={handleDrop}
                        >
                          {isDragOver && (
                            <div className="absolute inset-0 bg-blue-500/10 rounded-lg flex items-center justify-center">
                              <div className="text-center">
                                <div className="text-3xl mb-2 animate-bounce">📁</div>
                                <p className="text-blue-600 dark:text-blue-400 font-medium">
                                  松开鼠标上传文件
                                </p>
                              </div>
                            </div>
                          )}

                          <div className="text-center">
                            {((!batchMode && !file) || (batchMode && files.length === 0)) ? (
                              // 空状态：显示大的拖拽区域
                              <>
                                <div className="text-4xl mb-4">📁</div>
                                <h3 className="text-lg font-medium text-slate-700 dark:text-slate-300 mb-2">
                                  拖拽图片到这里
                                </h3>
                                <p className="text-sm text-slate-500 dark:text-slate-400 mb-4">
                                  {batchMode ? "支持多张图片批量上传" : "支持单张图片上传"}
                                </p>
                              </>
                            ) : (
                              // 有文件状态：显示小的拖拽区域
                              <>
                                <div className="text-2xl mb-2">📁</div>
                                <p className="text-sm text-slate-600 dark:text-slate-400 mb-2">
                                  拖拽图片到这里，或者
                                </p>
                              </>
                            )}

                            <Input
                              id="file"
                              type="file"
                              accept="image/png,image/jpeg,image/webp"
                              multiple={batchMode}
                              onChange={(e) => {
                                if (batchMode) {
                                  if (e.target.files && e.target.files.length > 0) {
                                    handleBatchFileUpload(e.target.files)
                                  }
                                } else {
                                  setFile(e.target.files?.[0] || null)
                                }
                              }}
                              className="hidden"
                            />
                            <Button
                              type="button"
                              variant="outline"
                              onClick={() => document.getElementById('file')?.click()}
                              className="text-sm"
                            >
                              点击选择文件
                            </Button>
                            <p className="text-xs text-slate-500 dark:text-slate-400 mt-2">
                              支持 PNG、JPEG、WebP 格式
                              {batchMode && "，可选择多张图片"}
                            </p>
                          </div>
                        </div>

                        {/* 单文件模式显示 */}
                        {!batchMode && file && (
                          <div className="flex flex-wrap gap-2 pt-1">
                            <Badge variant="secondary" className="max-w-[18rem] truncate bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">{file.name}</Badge>
                            <Badge variant="outline" className="border-slate-300 text-slate-600 dark:border-slate-600 dark:text-slate-300">{formatBytes(file.size)}</Badge>
                          </div>
                        )}

                        {/* 批量模式显示 */}
                        {batchMode && files.length > 0 && (
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-slate-600 dark:text-slate-400">
                                已选择 {files.length} 张图片
                              </span>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={clearBatchFiles}
                                className="h-6 px-2 text-xs"
                              >
                                清空
                              </Button>
                            </div>
                            <div className="max-h-32 overflow-y-auto space-y-1">
                              {files.map((batchFile) => (
                                <div key={batchFile.id} className="flex items-center justify-between p-2 bg-slate-50 dark:bg-slate-800 rounded text-xs">
                                  <div className="flex items-center gap-2 flex-1 min-w-0">
                                    <div className={`w-2 h-2 rounded-full ${
                                      batchFile.status === 'completed' ? 'bg-green-500' :
                                      batchFile.status === 'processing' ? 'bg-blue-500' :
                                      batchFile.status === 'error' ? 'bg-red-500' :
                                      'bg-gray-400'
                                    }`} />
                                    <span className="truncate">{batchFile.file.name}</span>
                                    <Badge variant="outline" className="text-[10px] px-1 py-0">
                                      {formatBytes(batchFile.file.size)}
                                    </Badge>
                                  </div>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => removeBatchFile(batchFile.id)}
                                    className="h-4 w-4 p-0 text-slate-400 hover:text-red-500"
                                  >
                                    ×
                                  </Button>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                  </CardContent>
                </Card>

                <Card className="shadow-sm border bg-white dark:bg-slate-900">
                  <CardHeader>
                    <CardTitle className="text-lg font-semibold text-slate-900 dark:text-white">参数设置</CardTitle>
                    <CardDescription className="text-slate-600 dark:text-slate-300">选择拆分模式、网格、尺寸、格式与命名规则</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* 优化的拆分模式选择 */}
                    <div className="space-y-3">
                      <Label className="text-base font-semibold text-slate-800 dark:text-slate-200 flex items-center gap-2">
                        <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                        拆分模式
                      </Label>
                      <RadioGroup
                        className="grid grid-cols-1 gap-3"
                        value={mode}
                        onValueChange={(v) => setMode(v as any)}
                      >
                        <div className="flex items-center gap-3 p-4 border-2 border-slate-200 dark:border-slate-700 rounded-xl hover:border-purple-300 dark:hover:border-purple-600 transition-colors data-[state=checked]:border-purple-500 data-[state=checked]:bg-purple-50 dark:data-[state=checked]:bg-purple-950/20">
                          <RadioGroupItem id="mode-count" value="count" className="border-purple-500" />
                          <div className="flex-1">
                            <Label htmlFor="mode-count" className="cursor-pointer text-slate-800 dark:text-slate-200 font-medium">按总数量</Label>
                            <p className="text-xs text-slate-600 dark:text-slate-400">自动计算接近方形的行列数</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3 p-4 border-2 border-slate-200 dark:border-slate-700 rounded-xl hover:border-purple-300 dark:hover:border-purple-600 transition-colors data-[state=checked]:border-purple-500 data-[state=checked]:bg-purple-50 dark:data-[state=checked]:bg-purple-950/20">
                          <RadioGroupItem id="mode-grid" value="grid" className="border-purple-500" />
                          <div className="flex-1">
                            <Label htmlFor="mode-grid" className="cursor-pointer text-slate-800 dark:text-slate-200 font-medium">自定义网格</Label>
                            <p className="text-xs text-slate-600 dark:text-slate-400">手动指定行数和列数</p>
                          </div>
                        </div>
                      </RadioGroup>
                    </div>

                    {/* 动态参数输入区域 */}
                    <div className="bg-gradient-to-r from-slate-50 to-blue-50 dark:from-slate-800/50 dark:to-blue-900/20 rounded-xl p-4 border border-slate-200 dark:border-slate-700">
                      {mode === "count" ? (
                        <div className="space-y-3">
                          <Label htmlFor="count" className="text-base font-medium text-slate-800 dark:text-slate-200 flex items-center gap-2">
                            <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                            总块数
                          </Label>
                          <div className="flex items-center gap-4">
                            <Input
                              id="count"
                              className="w-24 h-12 text-lg font-semibold text-center border-2 border-blue-200 dark:border-blue-700 focus:border-blue-500 dark:focus:border-blue-400 rounded-xl"
                              type="number"
                              min={1}
                              value={count}
                              onChange={(e) => setCount(parseInt(e.target.value || "1", 10))}
                            />
                            <div className="flex-1">
                              <div className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                自动计算为：{computedGrid.rows} × {computedGrid.cols}
                              </div>
                              <p className="text-xs text-slate-500 dark:text-slate-400">系统会自动计算接近方形的行列数</p>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-3">
                          <Label className="text-base font-medium text-slate-800 dark:text-slate-200 flex items-center gap-2">
                            <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                            网格尺寸
                          </Label>
                          <div className="flex items-center gap-4">
                            <div className="flex items-center gap-2">
                              <div className="space-y-1">
                                <Label htmlFor="rows" className="text-xs text-slate-600 dark:text-slate-400">行数</Label>
                                <Input
                                  id="rows"
                                  className="w-20 h-12 text-lg font-semibold text-center border-2 border-purple-200 dark:border-purple-700 focus:border-purple-500 dark:focus:border-purple-400 rounded-xl"
                                  type="number"
                                  min={1}
                                  value={rows}
                                  onChange={(e) => setRows(parseInt(e.target.value || "1", 10))}
                                />
                              </div>
                              <span className="text-2xl text-slate-400 mt-6">×</span>
                              <div className="space-y-1">
                                <Label htmlFor="cols" className="text-xs text-slate-600 dark:text-slate-400">列数</Label>
                                <Input
                                  id="cols"
                                  className="w-20 h-12 text-lg font-semibold text-center border-2 border-purple-200 dark:border-purple-700 focus:border-purple-500 dark:focus:border-purple-400 rounded-xl"
                                  type="number"
                                  min={1}
                                  value={cols}
                                  onChange={(e) => setCols(parseInt(e.target.value || "1", 10))}
                                />
                              </div>
                            </div>
                            <div className="flex-1">
                              <div className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                总计：{rows * cols} 张切片
                              </div>
                              <p className="text-xs text-slate-500 dark:text-slate-400">手动指定行数和列数</p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center justify-between">
                      <Label className="text-sm font-medium text-slate-700 dark:text-slate-200">显示网格线</Label>
                      <Switch checked={showGrid} onCheckedChange={setShowGrid} />
                    </div>

                    <div className="space-y-4 border-t border-slate-200 dark:border-slate-700 pt-4">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm font-medium text-slate-700 dark:text-slate-200">自定义切片尺寸</Label>
                        <Switch checked={useCustomSize} onCheckedChange={setUseCustomSize} />
                      </div>

                      {useCustomSize && (
                        <div className="space-y-3">
                          <div className="flex items-center gap-2">
                            <Label className="text-xs text-slate-600 dark:text-slate-400">保持比例</Label>
                            <Switch
                              checked={lockAspectRatio}
                              onCheckedChange={setLockAspectRatio}
                            />
                          </div>

                          <div className="grid grid-cols-2 gap-3">
                            <div className="space-y-1">
                              <Label htmlFor="custom-width" className="text-xs font-medium text-slate-600 dark:text-slate-400">宽度 (px)</Label>
                              <Input
                                id="custom-width"
                                type="number"
                                min={1}
                                value={customWidth}
                                onChange={(e) => handleCustomWidthChange(e.target.value ? parseInt(e.target.value, 10) : "")}
                                className="h-8 text-sm border-slate-200 dark:border-slate-700 focus:border-blue-500 dark:focus:border-blue-400"
                                placeholder="宽度"
                              />
                            </div>
                            <div className="space-y-1">
                              <Label htmlFor="custom-height" className="text-xs font-medium text-slate-600 dark:text-slate-400">高度 (px)</Label>
                              <Input
                                id="custom-height"
                                type="number"
                                min={1}
                                value={customHeight}
                                onChange={(e) => handleCustomHeightChange(e.target.value ? parseInt(e.target.value, 10) : "")}
                                className="h-8 text-sm border-slate-200 dark:border-slate-700 focus:border-blue-500 dark:focus:border-blue-400"
                                placeholder="高度"
                              />
                            </div>
                          </div>

                          {imgSize && (
                            <p className="text-xs text-slate-500 dark:text-slate-400">
                              原图尺寸：{imgSize.w} × {imgSize.h} px
                              {lockAspectRatio && typeof customWidth === "number" && typeof customHeight === "number" && (
                                <span className="ml-2">
                                  比例：{(customWidth / customHeight).toFixed(2)}:1
                                </span>
                              )}
                            </p>
                          )}
                        </div>
                      )}
                    </div>

                    <div className="flex gap-4 items-end">
                      <div className="space-y-2">
                        <Label className="text-sm font-medium text-slate-700 dark:text-slate-200">导出格式</Label>
                        <Select value={format} onValueChange={(v) => setFormat(v as any)}>
                          <SelectTrigger className="w-40 border-slate-200 dark:border-slate-700 focus:border-blue-500 dark:focus:border-blue-400">
                            <SelectValue placeholder="选择格式" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="image/png">PNG（无损）</SelectItem>
                            <SelectItem value="image/jpeg">JPEG（有损）</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      {format === "image/jpeg" && (
                        <div className="space-y-2">
                          <Label htmlFor="quality" className="text-sm font-medium text-slate-700 dark:text-slate-200">JPEG 质量（0-1）</Label>
                          <Input
                            id="quality"
                            className="w-40 border-slate-200 dark:border-slate-700 focus:border-blue-500 dark:focus:border-blue-400"
                            type="number"
                            min={0}
                            max={1}
                            step={0.01}
                            value={quality}
                            onChange={(e) => setQuality(Number(e.target.value))}
                          />
                        </div>
                      )}
                    </div>

                    {/* 智能命名规则 */}
                    <div className="space-y-4 border-t border-slate-200 dark:border-slate-700 pt-4">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm font-medium text-slate-700 dark:text-slate-200">自定义文件命名</Label>
                        <Switch checked={useCustomNaming} onCheckedChange={setUseCustomNaming} />
                      </div>

                      {useCustomNaming && (
                        <div className="space-y-3">
                          {/* 预设模板 */}
                          <div className="space-y-2">
                            <Label className="text-xs font-medium text-slate-600 dark:text-slate-400">快速选择</Label>
                            <div className="grid grid-cols-2 gap-2">
                              {namingPresets.map((preset) => (
                                <Button
                                  key={preset.name}
                                  variant={namingTemplate === preset.template ? "default" : "outline"}
                                  size="sm"
                                  onClick={() => setNamingTemplate(preset.template)}
                                  className="text-xs h-8"
                                >
                                  {preset.name}
                                </Button>
                              ))}
                            </div>
                          </div>

                          {/* 自定义模板 */}
                          <div className="space-y-2">
                            <Label htmlFor="naming-template" className="text-xs font-medium text-slate-600 dark:text-slate-400">
                              命名模板
                            </Label>
                            <Input
                              id="naming-template"
                              value={namingTemplate}
                              onChange={(e) => setNamingTemplate(e.target.value)}
                              placeholder="slice_{index}_{row}x{col}"
                              className="text-sm border-slate-200 dark:border-slate-700 focus:border-blue-500 dark:focus:border-blue-400"
                            />
                          </div>

                          {/* 包含原始文件名选项 */}
                          <div className="flex items-center justify-between">
                            <Label className="text-xs text-slate-600 dark:text-slate-400">包含原始文件名</Label>
                            <Switch
                              checked={includeOriginalName}
                              onCheckedChange={setIncludeOriginalName}
                            />
                          </div>

                          {/* 可用变量说明 */}
                          <div className="bg-slate-50 dark:bg-slate-800 rounded-lg p-3">
                            <p className="text-xs font-medium text-slate-700 dark:text-slate-300 mb-2">可用变量：</p>
                            <div className="grid grid-cols-2 gap-1 text-xs text-slate-600 dark:text-slate-400">
                              <span><code className="bg-slate-200 dark:bg-slate-700 px-1 rounded">{"{index}"}</code> 序号</span>
                              <span><code className="bg-slate-200 dark:bg-slate-700 px-1 rounded">{"{row}"}</code> 行号</span>
                              <span><code className="bg-slate-200 dark:bg-slate-700 px-1 rounded">{"{col}"}</code> 列号</span>
                              <span><code className="bg-slate-200 dark:bg-slate-700 px-1 rounded">{"{width}"}</code> 宽度</span>
                              <span><code className="bg-slate-200 dark:bg-slate-700 px-1 rounded">{"{height}"}</code> 高度</span>
                              <span><code className="bg-slate-200 dark:bg-slate-700 px-1 rounded">{"{timestamp}"}</code> 时间戳</span>
                              {includeOriginalName && (
                                <span><code className="bg-slate-200 dark:bg-slate-700 px-1 rounded">{"{original}"}</code> 原文件名</span>
                              )}
                            </div>
                          </div>

                          {/* 预览效果 */}
                          <div className="bg-blue-50 dark:bg-blue-950/20 rounded-lg p-3">
                            <p className="text-xs font-medium text-blue-700 dark:text-blue-300 mb-1">预览效果：</p>
                            <div className="space-y-1">
                              <code className="text-xs text-blue-600 dark:text-blue-400 break-all block">
                                第1张：{previewFileName(namingTemplate)}
                              </code>
                              {computedGrid.rows > 1 || computedGrid.cols > 1 ? (
                                <code className="text-xs text-blue-500 dark:text-blue-500 break-all block">
                                  第{computedGrid.rows * computedGrid.cols}张：{(() => {
                                    const now = new Date()
                                    const timestamp = now.toISOString().replace(/[:.]/g, "-").slice(0, 19)
                                    return generateFileName(
                                      namingTemplate,
                                      {
                                        index: computedGrid.rows * computedGrid.cols,
                                        row: computedGrid.rows,
                                        col: computedGrid.cols,
                                        width: useCustomSize && typeof customWidth === "number" ? customWidth : 400,
                                        height: useCustomSize && typeof customHeight === "number" ? customHeight : 300,
                                        timestamp,
                                        originalName: batchMode ? "example.jpg" : (file?.name || "image.jpg")
                                      },
                                      ".png"
                                    )
                                  })()}
                                </code>
                              ) : null}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {error && (
                      <div className="rounded-md border border-red-200 bg-red-50 text-red-700 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300 text-sm px-3 py-2">
                        {error}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* 中间：预览区域 */}
              <div className="xl:col-span-4">
                <Card className="shadow-sm border bg-white dark:bg-slate-900">
                  <CardHeader>
                    <CardTitle className="text-lg font-semibold text-slate-900 dark:text-white">
                      {batchMode ? "批量文件预览" : "原图网格预览"}
                    </CardTitle>
                    <CardDescription className="text-slate-600 dark:text-slate-300">
                      {batchMode ? "查看所有待处理的图片" : "实时预览切分效果"}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {batchMode ? (
                      // 批量模式预览
                      <div className="space-y-4">
                        {files.length === 0 ? (
                          <div className="text-center py-8 text-slate-500 dark:text-slate-400">
                            请选择图片文件
                          </div>
                        ) : (
                          <div className="space-y-3">
                            <div className="text-sm text-slate-600 dark:text-slate-400">
                              共 {files.length} 张图片，网格：{computedGrid.rows} × {computedGrid.cols}
                            </div>
                            <div className="max-h-96 overflow-y-auto space-y-2">
                              {files.map((batchFile) => (
                                <div key={batchFile.id} className="border border-slate-200 dark:border-slate-700 rounded-lg p-3">
                                  <div className="flex items-center gap-3 mb-2">
                                    <div className={`w-3 h-3 rounded-full ${
                                      batchFile.status === 'completed' ? 'bg-green-500' :
                                      batchFile.status === 'processing' ? 'bg-blue-500 animate-pulse' :
                                      batchFile.status === 'error' ? 'bg-red-500' :
                                      'bg-gray-400'
                                    }`} />
                                    <span className="text-sm font-medium truncate">{batchFile.file.name}</span>
                                    {batchFile.imgSize && (
                                      <span className="text-xs text-slate-500 dark:text-slate-400">
                                        {batchFile.imgSize.w} × {batchFile.imgSize.h}
                                      </span>
                                    )}
                                  </div>
                                  <div className="relative bg-slate-100 dark:bg-slate-800 rounded overflow-hidden">
                                    <img
                                      src={batchFile.imageUrl}
                                      alt={batchFile.file.name}
                                      className="w-full h-24 object-contain"
                                    />
                                    {showGrid && batchFile.imgSize && (
                                      <div className="pointer-events-none absolute inset-0">
                                        {/* 横线 */}
                                        {gridLines.hs.map((p, i) => (
                                          <div
                                            key={`h-${i}`}
                                            className="absolute left-0 right-0 bg-blue-500/40 dark:bg-blue-400/40"
                                            style={{ top: `${p}%`, height: 1 }}
                                          />
                                        ))}
                                        {/* 竖线 */}
                                        {gridLines.vs.map((p, i) => (
                                          <div
                                            key={`v-${i}`}
                                            className="absolute top-0 bottom-0 bg-blue-500/40 dark:bg-blue-400/40"
                                            style={{ left: `${p}%`, width: 1 }}
                                          />
                                        ))}
                                      </div>
                                    )}
                                  </div>
                                  {batchFile.status === 'error' && (
                                    <p className="text-xs text-red-500 mt-1">{batchFile.error}</p>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    ) : (
                      // 单文件模式预览
                      <div>
                        <div className="relative bg-slate-100 dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg overflow-hidden">
                          {imageUrl && (
                            <img src={imageUrl} alt="original" className="w-full h-auto block" />
                          )}
                          {showGrid && (
                          <div className="pointer-events-none absolute inset-0">
                            {/* 横线 */}
                            {gridLines.hs.map((p, i) => (
                              <div
                                key={`h-${i}`}
                                className="absolute left-0 right-0 bg-blue-500/40 dark:bg-blue-400/40"
                                style={{ top: `${p}%`, height: 1 }}
                              />
                            ))}
                            {/* 竖线 */}
                            {gridLines.vs.map((p, i) => (
                              <div
                                key={`v-${i}`}
                                className="absolute top-0 bottom-0 bg-blue-500/40 dark:bg-blue-400/40"
                                style={{ left: `${p}%`, width: 1 }}
                              />
                            ))}
                          </div>
                          )}
                        </div>
                        <p className="text-xs text-slate-500 dark:text-slate-400 mt-2">
                          原图尺寸：{imgSize?.w ?? "-"} × {imgSize?.h ?? "-"}，网格：{computedGrid.rows} × {computedGrid.cols}
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* 右侧：缩略图和操作区域 */}
              <div className="xl:col-span-4 space-y-6">
                <Card className="shadow-sm border bg-white dark:bg-slate-900">
                  <CardHeader>
                    <CardTitle className="text-lg font-semibold text-slate-900 dark:text-white">切片预览</CardTitle>
                    {!previewing && previewSlices.length > 0 && (
                      <CardDescription className="text-slate-600 dark:text-slate-300">
                        {previewSlices.length} 张切片 · {computedGrid.rows} × {computedGrid.cols} 网格
                      </CardDescription>
                    )}
                  </CardHeader>
                  <CardContent>
                    {previewing && (
                      <div className="flex items-center justify-center py-12">
                        <div className="text-center">
                          <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-3"></div>
                          <div className="text-sm text-slate-600 dark:text-slate-400">生成预览中...</div>
                        </div>
                      </div>
                    )}
                    {!previewing && previewSlices.length === 0 && (
                      <div className="flex items-center justify-center py-12">
                        <div className="text-center">
                          <div className="text-4xl mb-3">🖼️</div>
                          <div className="text-sm text-slate-500 dark:text-slate-400">暂无预览</div>
                          <div className="text-xs text-slate-400 dark:text-slate-500 mt-1">请先选择图片</div>
                        </div>
                      </div>
                    )}
                    {!previewing && previewSlices.length > 0 && (
                      <div className="overflow-auto max-h-[50vh] overscroll-contain rounded-xl border-2 border-slate-200 dark:border-slate-700 bg-gradient-to-br from-slate-50 to-blue-50/30 dark:from-slate-800/50 dark:to-blue-900/20 p-4">
                        <div
                          className="grid gap-4"
                          style={{ gridTemplateColumns: `repeat(${Math.min(computedGrid.cols, 4)}, minmax(120px, 1fr))` }}
                        >
                          {previewSlices.map((slice, i) => (
                            <div key={i} className="group relative rounded-xl border-2 border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900 overflow-hidden hover:shadow-xl hover:scale-105 transition-all duration-300 hover:border-orange-300 dark:hover:border-orange-600">
                              <img src={slice.url} alt={`slice-${i + 1}`} className="w-full h-24 object-contain bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-800 dark:to-slate-700" />
                              <div className="absolute left-2 top-2 text-[10px] leading-none bg-gradient-to-r from-slate-900 to-slate-700 text-white px-2 py-1 rounded-lg font-bold shadow-lg">
                                #{String(i + 1).padStart(2, "0")}
                              </div>
                              <div className="absolute right-2 bottom-2 text-[9px] leading-none bg-gradient-to-r from-orange-500 to-red-500 text-white px-2 py-1 rounded-lg font-medium shadow-lg">
                                {slice.width} × {slice.height}
                              </div>
                              <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* 优化的操作区域 */}
                <div className="space-y-6">
                  {/* 增强的进度显示 */}
                  {batchProgress && (
                    <Card className="border-2 border-blue-200 dark:border-blue-800 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30">
                      <CardContent className="p-4">
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                              <span className="font-semibold text-blue-800 dark:text-blue-200">处理进度</span>
                            </div>
                            <span className="font-bold text-lg text-blue-700 dark:text-blue-300">
                              {batchProgress.current} / {batchProgress.total}
                            </span>
                          </div>
                          <div className="w-full bg-blue-200 dark:bg-blue-800 rounded-full h-3 overflow-hidden">
                            <div
                              className="bg-gradient-to-r from-blue-500 to-indigo-600 h-3 rounded-full transition-all duration-500 ease-out"
                              style={{ width: `${(batchProgress.current / batchProgress.total) * 100}%` }}
                            />
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-xs text-blue-600 dark:text-blue-400">正在处理：</span>
                            <span className="text-xs font-medium text-blue-800 dark:text-blue-200 truncate flex-1">
                              {batchProgress.currentFileName}
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* 操作按钮 */}
                  <Button
                    onClick={batchMode ? batchSplitAndZip : splitAndZip}
                    disabled={loading || (batchMode ? files.length === 0 : !file)}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
                  >
                    {loading ? (
                      <div className="flex items-center justify-center gap-2">
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        <span>
                          {batchProgress ? `处理中 (${batchProgress.current}/${batchProgress.total})` : "处理中..."}
                        </span>
                      </div>
                    ) : (
                      batchMode ? "批量生成并下载 ZIP" : "生成并下载 ZIP"
                    )}
                  </Button>

                  {/* 输出信息 */}
                  <Card className="bg-slate-50 dark:bg-slate-800 border border-slate-200 dark:border-slate-700">
                      <CardContent className="p-4">
                        <div className="space-y-3 text-sm text-slate-600 dark:text-slate-400">
                          {batchMode ? (
                            <div className="space-y-2">
                              <div className="flex justify-between">
                                <span>批量处理：</span>
                                <span className="font-medium">{files.length} 张图片</span>
                              </div>
                              <div className="flex justify-between">
                                <span>每张输出：</span>
                                <span className="font-medium">{computedGrid.rows} × {computedGrid.cols} = {computedGrid.rows * computedGrid.cols} 张</span>
                              </div>
                              <div className="flex justify-between border-t pt-2">
                                <span>总计切片：</span>
                                <span className="font-bold text-blue-600 dark:text-blue-400">
                                  {files.length * computedGrid.rows * computedGrid.cols} 张
                                </span>
                              </div>
                            </div>
                          ) : (
                            <div className="flex justify-between">
                              <span>将输出：</span>
                              <span className="font-medium">{computedGrid.rows} × {computedGrid.cols} = {computedGrid.rows * computedGrid.cols} 张</span>
                            </div>
                          )}

                          {useCustomSize && typeof customWidth === "number" && typeof customHeight === "number" && (
                            <div className="flex justify-between">
                              <span>每张尺寸：</span>
                              <span className="font-medium">{customWidth} × {customHeight} px</span>
                            </div>
                          )}

                          {useCustomNaming && (
                            <div>
                              <div className="mb-1">命名规则：</div>
                              <code className="text-xs font-mono bg-slate-100 dark:bg-slate-700 px-2 py-1 rounded break-all">
                                {namingTemplate}
                              </code>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

function pad(n: number, len: number) {
  return String(n).padStart(len, "0")
}

function loadImage(url: string) {
  return new Promise<HTMLImageElement>((resolve, reject) => {
    const img = new Image()
    img.crossOrigin = "anonymous"
    img.onload = () => resolve(img)
    img.onerror = reject
    img.src = url
  })
}

function computeSliceBounds(
  imgW: number,
  imgH: number,
  cols: number,
  rows: number,
  colIndex: number,
  rowIndex: number
) {
  // 使用整除分配余数，保证每块刚好覆盖整张图且无缝
  const sx = Math.floor((colIndex * imgW) / cols)
  const ex = Math.floor(((colIndex + 1) * imgW) / cols)
  const sy = Math.floor((rowIndex * imgH) / rows)
  const ey = Math.floor(((rowIndex + 1) * imgH) / rows)
  return { sx, sy, sw: ex - sx, sh: ey - sy }
}

async function sliceToBlob(
  img: HTMLImageElement,
  sx: number,
  sy: number,
  sw: number,
  sh: number,
  type: "image/png" | "image/jpeg",
  quality: number,
  targetWidth?: number,
  targetHeight?: number
): Promise<Blob> {
  const canvas = document.createElement("canvas")

  // 如果指定了目标尺寸，使用目标尺寸；否则使用原始切片尺寸
  const outputWidth = targetWidth || sw
  const outputHeight = targetHeight || sh

  canvas.width = outputWidth
  canvas.height = outputHeight
  const ctx = canvas.getContext("2d")!
  ctx.imageSmoothingEnabled = true
  ctx.imageSmoothingQuality = "high"
  ctx.drawImage(img, sx, sy, sw, sh, 0, 0, outputWidth, outputHeight)

  const blob = await new Promise<Blob>((resolve, reject) => {
    canvas.toBlob((b) => (b ? resolve(b) : reject(new Error("导出失败"))), type, quality)
  })
  return blob
}

function formatBytes(bytes: number) {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${(bytes / Math.pow(k, i)).toFixed(i === 0 ? 0 : 2)} ${sizes[i]}`
}

